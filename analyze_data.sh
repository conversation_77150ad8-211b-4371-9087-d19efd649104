#!/bin/bash

# <PERSON>ript to analyze CSV data and calculate statistics by client
# Usage: ./analyze_data.sh input_file.csv [records_per_client]

if [ $# -eq 0 ]; then
    echo "Usage: $0 <csv_file> [records_per_client]"
    echo "  csv_file: Input CSV file with test_type,latency columns"
    echo "  records_per_client: Number of records per client (default: 1799)"
    exit 1
fi

INPUT_FILE="$1"
RECORDS_PER_CLIENT=${2:-1799}  # Default to 1799 records per client

if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: File $INPUT_FILE not found"
    exit 1
fi

echo "test_type,client_name,total_records,mean_latency,std_latency"

# Process the CSV file and calculate statistics for each client
awk -F',' -v records_per_client="$RECORDS_PER_CLIENT" '
BEGIN {
    OFS=","
    client_counter = 40  # Start from user40
    current_client = "user" client_counter
    record_count = 0
}
NR > 1 {  # Skip header row
    test_type = $1
    latency = $2

    # Check if we need to move to next client
    if (record_count >= records_per_client) {
        # Calculate and output statistics for current client
        if (record_count > 0) {
            mean = sum / record_count
            variance = (sum_sq / record_count) - (mean * mean)
            std_dev = sqrt(variance > 0 ? variance : 0)
            printf "%s,%s,%d,%.15g,%.15g\n", current_test_type, current_client, record_count, mean, std_dev
        }

        # Reset for next client
        client_counter++
        current_client = "user" client_counter
        record_count = 0
        sum = 0
        sum_sq = 0
    }

    # Accumulate statistics for current client
    record_count++
    sum += latency
    sum_sq += latency * latency
    current_test_type = test_type
}
END {
    # Output statistics for the last client
    if (record_count > 0) {
        mean = sum / record_count
        variance = (sum_sq / record_count) - (mean * mean)
        std_dev = sqrt(variance > 0 ? variance : 0)
        printf "%s,%s,%d,%.15g,%.15g\n", current_test_type, current_client, record_count, mean, std_dev
    }
}' "$INPUT_FILE"