#!/bin/bash

# Script to analyze CSV data and calculate statistics by client
# Usage: ./analyze_data.sh input_file.csv

if [ $# -eq 0 ]; then
    echo "Usage: $0 <csv_file>"
    exit 1
fi

INPUT_FILE="$1"

if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: File $INPUT_FILE not found"
    exit 1
fi

echo "test_type,client_name,total_records,mean_latency,std_latency"

# Process the CSV file and calculate statistics for each client
awk -F',' '
BEGIN { OFS="," }
NR > 1 {  # Skip header row
    client = $2
    latency = $4
    
    # Count records and sum latencies for each client
    count[client]++
    sum[client] += latency
    sum_sq[client] += latency * latency
    
    # Store test_type for each client (assuming same test_type per client)
    if (!(client in test_type)) {
        test_type[client] = $1
    }
}
END {
    for (client in count) {
        n = count[client]
        mean = sum[client] / n
        
        # Calculate standard deviation
        variance = (sum_sq[client] / n) - (mean * mean)
        std_dev = sqrt(variance > 0 ? variance : 0)
        
        printf "%s,%s,%d,%.15g,%.15g\n", test_type[client], client, n, mean, std_dev
    }
}' "$INPUT_FILE" | sort -t',' -k2